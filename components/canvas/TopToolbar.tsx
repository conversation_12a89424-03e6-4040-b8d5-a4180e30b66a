import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { Copy, Crop, Download, Trash2, Group } from "lucide-react";
import type { PlacedImage } from "@/@types/canvas";
import { Icons } from "../icon/icons";
import { downloadImages } from "@/lib/utils-download";
import { toast } from "sonner";

interface TopToolbarProps {
	selectedIds: string[];
	images: PlacedImage[];
	handleDuplicate: () => void;
	handleRemoveBackground: () => void;
	handleCombineImages: () => void;
	handleDelete: () => void;
	setCroppingImageId: (id: string | null) => void;
}

export const TopToolbar: React.FC<TopToolbarProps> = ({
	selectedIds,
	images,
	handleDuplicate,
	handleRemoveBackground,
	handleCombineImages,
	handleDelete,
	setCroppingImageId,
}) => {
	const hasSelectedImages = selectedIds.length > 0;
	const hasSingleSelected = selectedIds.length === 1;
	const hasMultipleSelected = selectedIds.length > 1;

	// Don't show toolbar if no images are selected
	if (!hasSelectedImages) {
		return null;
	}

	const handleDownload = async () => {
		try {
			const imagesToDownload = selectedIds
				.map((id) => images.find((img) => img.id === id))
				.filter((img): img is PlacedImage => img !== undefined)
				.map((img, index) => ({
					src: img.src,
					fileName: `image-${Date.now()}-${index + 1}`,
				}));

			if (imagesToDownload.length > 0) {
				await downloadImages(imagesToDownload);
			}
		} catch (error) {
			console.error("Download failed:", error);
			toast.error("Download failed");
		}
	};

	return (
		<TooltipProvider>
			<div
				className={cn(
					"absolute top-4 left-1/2 z-30 hidden -translate-x-1/2 md:flex",
					"rounded-xl border border-gray-200 bg-white/90 shadow-lg backdrop-blur-sm",
					"items-center gap-1 p-2",
					"transition-all duration-300 ease-in-out",
					hasSelectedImages ? "translate-y-0 opacity-100" : "pointer-events-none -translate-y-2 opacity-0",
				)}
			>
				{/* Single image actions */}
				{hasSingleSelected && (
					<>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button variant="ghost" size="icon" onClick={handleDuplicate} className="size-8 text-neutral-700">
									<Copy className="size-3.5" />
								</Button>
							</TooltipTrigger>
							<TooltipContent>
								<p>Duplicate</p>
							</TooltipContent>
						</Tooltip>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button variant="ghost" size="icon" onClick={() => setCroppingImageId(selectedIds[0])} className="size-8 text-neutral-700">
									<Crop className="size-4" />
								</Button>
							</TooltipTrigger>
							<TooltipContent>
								<p>Crop</p>
							</TooltipContent>
						</Tooltip>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button variant="ghost" size="icon" onClick={handleRemoveBackground} className="size-8 text-neutral-700">
									<Icons.BackgroundTransparent className="size-3.5" />
								</Button>
							</TooltipTrigger>
							<TooltipContent>
								<p>Remove Background</p>
							</TooltipContent>
						</Tooltip>
						<div className="mx-1 h-6 w-px bg-gray-300" />
					</>
				)}

				{/* Multiple images actions */}
				{hasMultipleSelected && (
					<>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button variant="ghost" size="icon" onClick={handleCombineImages} className="size-8 text-neutral-700">
									<Group className="size-4" />
								</Button>
							</TooltipTrigger>
							<TooltipContent>
								<p>Group</p>
							</TooltipContent>
						</Tooltip>
						<div className="mx-1 h-6 w-px bg-gray-300" />
					</>
				)}

				{/* Common actions for all selections */}
				<Tooltip>
					<TooltipTrigger asChild>
						<Button variant="ghost" size="icon" onClick={handleDownload} className="size-8 text-neutral-700">
							<Download className="size-4" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>
						<p>Download</p>
					</TooltipContent>
				</Tooltip>
				<Tooltip>
					<TooltipTrigger asChild>
						<Button variant="ghost" size="icon" onClick={handleDelete} className="size-8 text-red-600 hover:bg-red-50 hover:text-red-700">
							<Trash2 className="size-4" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>
						<p>Delete</p>
					</TooltipContent>
				</Tooltip>
			</div>
		</TooltipProvider>
	);
};
